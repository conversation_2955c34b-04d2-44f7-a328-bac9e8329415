# AI Trainer Frontend

Frontend aplikasi AI Trainer menggunakan Next.js 14 dengan TypeScript.

## Environment Configuration

### Setup Environment Variables

1. Copy file `.env.example` menjadi `.env.local`:
   ```bash
   cp .env.example .env.local
   ```

2. Edit file `.env.local` sesuai dengan konfigurasi Anda:

```bash
# Backend API Configuration
NEXT_PUBLIC_API_URL=http://localhost:3000/api

# WebSocket Configuration  
NEXT_PUBLIC_WS_URL=http://localhost:3000

# Development Configuration
NEXT_PUBLIC_NODE_ENV=development

# Optional: App Configuration
NEXT_PUBLIC_APP_NAME=AI Trainer Admin
NEXT_PUBLIC_APP_VERSION=1.0.0
```

### Environment Variables Explanation

| Variable | Description | Default |
|----------|-------------|---------|
| `NEXT_PUBLIC_API_URL` | Backend API base URL | `http://localhost:3000/api` |
| `NEXT_PUBLIC_WS_URL` | WebSocket server URL | `http://localhost:3000` |
| `NEXT_PUBLIC_NODE_ENV` | Environment mode | `development` |
| `NEXT_PUBLIC_APP_NAME` | Application name | `AI Trainer Admin` |
| `NEXT_PUBLIC_APP_VERSION` | Application version | `1.0.0` |

### Untuk Development

Jika backend berjalan di port yang berbeda, cukup update `.env.local`:

```bash
# Jika backend di port 3002
NEXT_PUBLIC_API_URL=http://localhost:3002/api
NEXT_PUBLIC_WS_URL=http://localhost:3002

# Jika backend di port 8000
NEXT_PUBLIC_API_URL=http://localhost:8000/api
NEXT_PUBLIC_WS_URL=http://localhost:8000
```

### Untuk Production

Untuk production, set environment variables di hosting provider atau gunakan `.env.production`:

```bash
NEXT_PUBLIC_API_URL=https://api.yourdomain.com/api
NEXT_PUBLIC_WS_URL=https://api.yourdomain.com
NEXT_PUBLIC_NODE_ENV=production
```

## Installation & Development

```bash
# Install dependencies
npm install

# Run development server
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

## Configuration Structure

Semua konfigurasi terpusat di `src/config/index.ts` yang menggunakan environment variables dengan fallback values yang aman.

## Features

- ✅ Centralized configuration
- ✅ Environment-based setup
- ✅ TypeScript support
- ✅ Auto-reconnecting WebSocket
- ✅ Token-based authentication
- ✅ Responsive design
- ✅ Real-time updates
