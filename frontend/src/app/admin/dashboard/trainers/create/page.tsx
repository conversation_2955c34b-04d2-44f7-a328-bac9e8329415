'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON>Left, Save, Eye, EyeOff } from 'lucide-react';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { api } from '@/lib/api';
import Link from 'next/link';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface TrainerFormData {
  name: string;
  systemPrompt: string;
  description: string;
  status: 'active' | 'inactive' | 'draft';
}

const CreateTrainerPage: React.FC = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [showPrompt, setShowPrompt] = useState(false);
  const [formData, setFormData] = useState<TrainerFormData>({
    name: '',
    systemPrompt: '',
    description: '',
    status: 'draft'
  });
  const [errors, setErrors] = useState<Partial<TrainerFormData>>({});

  const handleInputChange = (field: keyof TrainerFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<TrainerFormData> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    } else if (formData.name.length < 3) {
      newErrors.name = 'Name must be at least 3 characters';
    }

    if (!formData.systemPrompt.trim()) {
      newErrors.systemPrompt = 'System prompt is required';
    } else if (formData.systemPrompt.length < 10) {
      newErrors.systemPrompt = 'System prompt must be at least 10 characters';
    }

    if (formData.description && formData.description.length > 500) {
      newErrors.description = 'Description must be less than 500 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      await api.trainers.create(formData);
      router.push('/admin/dashboard/trainers');
    } catch (error: unknown) {
      console.error('Error creating trainer:', error);
      // Handle specific API errors
      if (error instanceof Error) {
        // A more robust error handling could be implemented here
        // For now, just log the message
        console.error(error.message);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/admin/dashboard/trainers">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Trainers
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Create New Trainer</h1>
            <p className="text-gray-600">Set up a new AI trainer with custom configuration</p>
          </div>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-8">
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-6">Basic Information</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="md:col-span-2">
              <Input
                label="Trainer Name"
                placeholder="Enter trainer name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                error={errors.name}
                helperText="A descriptive name for your AI trainer"
              />
            </div>

            <div className="md:col-span-2">
              <Input
                label="Description"
                placeholder="Enter trainer description (optional)"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                error={errors.description}
                helperText="Brief description of what this trainer does"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleInputChange('status', value)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-sm text-gray-500 mt-1">
                Draft trainers are not available to members
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold text-gray-900">System Prompt</h2>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setShowPrompt(!showPrompt)}
            >
              {showPrompt ? <EyeOff className="h-4 w-4 mr-2" /> : <Eye className="h-4 w-4 mr-2" />}
              {showPrompt ? 'Hide' : 'Show'} Prompt
            </Button>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                System Prompt
              </label>
              <textarea
                value={formData.systemPrompt}
                onChange={(e) => handleInputChange('systemPrompt', e.target.value)}
                placeholder="Enter the system prompt that defines how this AI trainer should behave..."
                rows={showPrompt ? 12 : 6}
                className={`block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm font-mono ${
                  !showPrompt ? 'text-transparent bg-gray-100' : ''
                }`}
                style={!showPrompt ? {
                  color: 'transparent',
                  textShadow: '0 0 5px rgba(0,0,0,0.5)',
                  backgroundColor: '#f9fafb'
                } : {}}
              />
              {errors.systemPrompt && (
                <p className="text-sm text-red-600 mt-1">{errors.systemPrompt}</p>
              )}
              <p className="text-sm text-gray-500 mt-1">
                This prompt defines the AI trainer&apos;s personality, knowledge, and behavior patterns
              </p>
            </div>

            <div className="bg-blue-50 p-4 rounded-md">
              <h3 className="text-sm font-medium text-blue-900 mb-2">Prompt Guidelines:</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Be specific about the trainer&apos;s role and expertise</li>
                <li>• Define the communication style and tone</li>
                <li>• Include any specific instructions or constraints</li>
                <li>• Consider the target audience (members)</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-4">
          <Link href="/admin/dashboard/trainers">
            <Button variant="outline" type="button">
              Cancel
            </Button>
          </Link>
          <Button type="submit" loading={loading}>
            <Save className="h-4 w-4 mr-2" />
            Create Trainer
          </Button>
        </div>
      </form>
    </div>
  );
};

export default CreateTrainerPage;