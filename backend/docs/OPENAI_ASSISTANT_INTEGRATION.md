# OpenAI Assistant API Integration

## Overview

This document describes the implementation of OpenAI Assistant API integration in the AI Trainer Platform. The integration provides advanced conversational AI capabilities with thread management, real-time communication, progress tracking, and achievement systems.

## Architecture

### Core Components

1. **OpenAI Assistant Service** (`src/services/openai/assistant.service.js`)
   - Manages OpenAI Assistant creation and configuration
   - <PERSON>les thread lifecycle (create, manage, archive)
   - Implements run polling for response handling
   - Manages function calls for progress tracking

2. **Database Models**
   - `OpenAIThread`: Stores thread information and metadata
   - `OpenAIMessage`: Stores all messages with progress/score data
   - Enhanced `TrainerScore`: Includes submodule-specific progress

3. **Enhanced Chat Service** (`src/services/chat.service.js`)
   - Integrates Assistant API with existing chat functionality
   - Provides backward compatibility with legacy chat
   - Manages submodule switching and context

4. **WebSocket Enhancements** (`src/services/websocket.service.js`)
   - Real-time conversation updates
   - Typing indicators
   - Progress notifications
   - Achievement alerts

5. **Notification System** (`src/services/notification.service.js`)
   - Achievement tracking and rewards
   - Progress notifications
   - Real-time alerts

## Key Features

### 1. Thread Management

Each member conversation with a trainer submodule gets its own OpenAI thread:

```javascript
// Create thread for member conversation
const threadResult = await assistantService.createThread(
  trainerId,
  submoduleId,
  memberId
);
```

### 2. Assistant Configuration

Assistants are created dynamically based on trainer submodules:

```javascript
const assistant = await openai.beta.assistants.create({
  name: `${submodule.name} - AI Trainer`,
  instructions: buildAssistantInstructions(submodule.systemPrompt),
  model: 'gpt-4o',
  tools: [
    {
      type: 'function',
      function: {
        name: 'update_progress',
        description: 'Update learner progress and score',
        parameters: {
          type: 'object',
          properties: {
            progress: { type: 'integer', minimum: 0, maximum: 100 },
            score: { type: 'number', minimum: 0, maximum: 10 },
            message: { type: 'string' }
          }
        }
      }
    }
  ]
});
```

### 3. Run Polling

Implements robust polling mechanism for Assistant responses:

```javascript
async function pollRun(threadId, runId) {
  const maxAttempts = 30;
  const pollInterval = 1000;

  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    const run = await openai.beta.threads.runs.retrieve(threadId, runId);
    
    if (['completed', 'failed', 'cancelled', 'expired', 'requires_action'].includes(run.status)) {
      return run;
    }
    
    await new Promise(resolve => setTimeout(resolve, pollInterval));
  }
  
  throw new Error('Run polling timeout');
}
```

### 4. Progress Tracking

Function calls enable structured progress tracking:

```javascript
// Assistant calls this function to update progress
{
  "name": "update_progress",
  "arguments": {
    "progress": 75,
    "score": 8.5,
    "message": "Great work! You're making excellent progress."
  }
}
```

### 5. Real-time Features

WebSocket integration provides:
- Live message updates
- Typing indicators
- Progress notifications
- Achievement alerts

## API Endpoints

### Assistant Conversation

#### Initialize Conversation
```http
POST /api/assistant/conversation/initialize
Content-Type: application/json
Authorization: Bearer <token>

{
  "trainerId": 1,
  "submoduleId": 2
}
```

#### Send Message
```http
POST /api/assistant/message/send
Content-Type: application/json
Authorization: Bearer <token>

{
  "threadId": 123,
  "message": "I need help understanding this concept."
}
```

#### Switch Submodule
```http
POST /api/assistant/submodule/switch
Content-Type: application/json
Authorization: Bearer <token>

{
  "currentThreadId": 123,
  "newSubmoduleId": 3
}
```

### Progress & History

#### Get Conversation History
```http
GET /api/assistant/conversation/history?trainerId=1&limit=50
Authorization: Bearer <token>
```

#### Get Progress Tracking
```http
GET /api/assistant/progress/tracking?trainerId=1
Authorization: Bearer <token>
```

#### Get Thread Statistics
```http
GET /api/assistant/thread/123/stats
Authorization: Bearer <token>
```

### Notifications & Achievements

#### Get Achievements
```http
GET /api/notifications/achievements
Authorization: Bearer <token>
```

#### Get Notifications
```http
GET /api/notifications?limit=20&unreadOnly=true
Authorization: Bearer <token>
```

#### Get Statistics
```http
GET /api/notifications/statistics
Authorization: Bearer <token>
```

## WebSocket Events

### Client to Server

```javascript
// Join conversation room
socket.emit('join-assistant-conversation', {
  threadId: 123,
  memberId: 456
});

// Typing indicators
socket.emit('typing-start', { threadId: 123, memberId: 456 });
socket.emit('typing-stop', { threadId: 123, memberId: 456 });
```

### Server to Client

```javascript
// New message received
socket.on('assistant-new-message', (data) => {
  console.log('New message:', data.message);
  console.log('Progress:', data.progress);
  console.log('Score:', data.score);
});

// Progress updated
socket.on('assistant-progress-updated', (data) => {
  updateProgressBar(data.progress);
});

// Achievement earned
socket.on('achievement-earned', (data) => {
  showAchievementNotification(data);
});

// Typing indicator
socket.on('assistant-typing', (data) => {
  showTypingIndicator(data.isTyping);
});
```

## Database Schema

### OpenAI Threads Table
```sql
CREATE TABLE openai_threads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    threadId VARCHAR(255) NOT NULL UNIQUE,
    trainerId INT NOT NULL,
    submoduleId INT NULL,
    memberId INT NOT NULL,
    assistantId VARCHAR(255) NULL,
    status ENUM('active', 'inactive', 'archived') DEFAULT 'active',
    lastMessageAt DATETIME NULL,
    metadata JSON NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (trainerId) REFERENCES trainers(id),
    FOREIGN KEY (submoduleId) REFERENCES trainer_submodules(id),
    FOREIGN KEY (memberId) REFERENCES members(id)
);
```

### OpenAI Messages Table
```sql
CREATE TABLE openai_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    messageId VARCHAR(255) NOT NULL UNIQUE,
    threadId INT NOT NULL,
    openaiThreadId VARCHAR(255) NOT NULL,
    runId VARCHAR(255) NULL,
    role ENUM('user', 'assistant') NOT NULL,
    content TEXT NOT NULL,
    aiProgress INT NULL CHECK (aiProgress >= 0 AND aiProgress <= 100),
    aiScore DECIMAL(3,1) NULL CHECK (aiScore >= 0 AND aiScore <= 10),
    status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
    metadata JSON NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (threadId) REFERENCES openai_threads(id)
);
```

## Achievement System

### Available Achievements

- **First Steps** (10 points): Send first message
- **Quarter Way** (25 points): Reach 25% progress
- **Halfway Hero** (50 points): Reach 50% progress
- **Almost There** (75 points): Reach 75% progress
- **Module Master** (100 points): Complete a module
- **Excellence** (50 points): Score 9+ on evaluation
- **Perfect Performance** (100 points): Score perfect 10
- **Getting Started** (30 points): 3-day learning streak
- **Week Warrior** (70 points): 7-day learning streak
- **Monthly Master** (300 points): 30-day learning streak
- **Diverse Learner** (75 points): Use 3 different trainers
- **Chatty Learner** (25 points): Send 10 messages
- **Active Participant** (100 points): Send 50 messages
- **Conversation Master** (200 points): Send 100 messages

### Achievement Tracking

```javascript
// Check achievements after events
await notificationService.checkAchievements(memberId, 'message_sent', eventData);
await notificationService.checkAchievements(memberId, 'progress_updated', eventData);
await notificationService.checkAchievements(memberId, 'score_received', eventData);
```

## Configuration

### Environment Variables

```env
# OpenAI Configuration
OPENAI_API_KEY=sk-...
OPENAI_MODEL=gpt-4o
OPENAI_MAX_TOKENS=4000

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=ai_trainer
DB_USER=root
DB_PASSWORD=password

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
```

### OpenAI Model Configuration

The system uses GPT-4o by default but can be configured to use other models:

```javascript
const config = {
  openai: {
    model: process.env.OPENAI_MODEL || 'gpt-4o',
    maxTokens: parseInt(process.env.OPENAI_MAX_TOKENS) || 4000,
    temperature: 0.7
  }
};
```

## Error Handling

### Circuit Breaker Pattern

The system implements circuit breaker pattern for OpenAI API calls:

```javascript
const circuitBreaker = circuitBreakerManager.getCircuitBreaker('openai-assistant', {
  failureThreshold: 5,
  resetTimeout: 60000,
  expectedErrors: [
    'rate limit exceeded',
    'quota exceeded',
    'invalid_request_error'
  ]
});
```

### Retry Logic

Automatic retry with exponential backoff:

```javascript
const retryHandler = new RetryHandler({
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffFactor: 2,
  retryCondition: (error) => {
    return error.code === 'ECONNREFUSED' ||
           error.code === 'ETIMEDOUT' ||
           (error.response && error.response.status >= 500);
  }
});
```

## Testing

### Frontend Testing

Access the test interface at: `http://localhost:3000/assistant-chat.html`

### API Testing

Use the provided Postman collection or test with curl:

```bash
# Login
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password","userType":"member"}'

# Initialize conversation
curl -X POST http://localhost:3000/api/assistant/conversation/initialize \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{"trainerId":1,"submoduleId":1}'

# Send message
curl -X POST http://localhost:3000/api/assistant/message/send \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{"threadId":1,"message":"Hello, I need help with this topic."}'
```

## Deployment

### Database Migration

Run the migration script to create required tables:

```bash
mysql -u root -p ai_trainer < migrations/create-openai-tables.sql
```

### Dependencies

Ensure all required packages are installed:

```bash
npm install openai@latest
```

### Production Considerations

1. **Rate Limiting**: OpenAI API has rate limits, implement appropriate throttling
2. **Monitoring**: Set up logging and monitoring for API calls
3. **Caching**: Use Redis for caching assistant configurations
4. **Backup**: Regular backup of conversation threads and messages
5. **Security**: Secure API keys and implement proper authentication

## Troubleshooting

### Common Issues

1. **Thread Creation Fails**
   - Check OpenAI API key validity
   - Verify trainer submodule exists and is active
   - Check database connectivity

2. **Message Sending Timeout**
   - Increase polling timeout
   - Check OpenAI API status
   - Verify network connectivity

3. **Progress Not Updating**
   - Ensure function call is properly configured
   - Check assistant instructions include progress tracking
   - Verify database schema includes progress fields

4. **WebSocket Connection Issues**
   - Check Socket.IO configuration
   - Verify authentication middleware
   - Check CORS settings

### Debug Mode

Enable debug logging:

```javascript
const logger = require('./utils/logger');
logger.level = 'debug';
```

## Future Enhancements

1. **Voice Integration**: Add speech-to-text and text-to-speech
2. **File Attachments**: Support for document and image uploads
3. **Advanced Analytics**: Detailed conversation analytics
4. **Multi-language Support**: Internationalization
5. **Mobile App**: Native mobile application
6. **Offline Support**: Cached responses for offline use

## Support

For technical support or questions:
- Check the API documentation at `/api-docs`
- Review logs in the `logs/` directory
- Contact the development team

---

*Last updated: December 2024* 