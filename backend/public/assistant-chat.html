<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Assistant Chat - Meet Trainer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .chat-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 800px;
            height: 600px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .chat-header .subtitle {
            opacity: 0.9;
            font-size: 14px;
        }

        .connection-status {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ff4444;
            animation: pulse 2s infinite;
        }

        .status-dot.connected {
            background: #44ff44;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .trainer-selector {
            padding: 15px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .trainer-selector select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            background: white;
            cursor: pointer;
        }

        .progress-bar {
            flex: 1;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 4px;
        }

        .progress-text {
            font-size: 12px;
            color: #666;
            min-width: 40px;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 14px;
        }

        .message.user .message-avatar {
            background: #667eea;
        }

        .message.assistant .message-avatar {
            background: #764ba2;
        }

        .message-content {
            flex: 1;
            max-width: 70%;
        }

        .message-bubble {
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.4;
            word-wrap: break-word;
        }

        .message.user .message-bubble {
            background: #667eea;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.assistant .message-bubble {
            background: white;
            color: #333;
            border: 1px solid #e9ecef;
            border-bottom-left-radius: 4px;
        }

        .message-meta {
            font-size: 11px;
            color: #666;
            margin-top: 4px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .message.user .message-meta {
            justify-content: flex-end;
        }

        .score-badge {
            background: #28a745;
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
        }

        .typing-indicator {
            display: none;
            align-items: center;
            gap: 8px;
            padding: 12px 16px;
            background: white;
            border-radius: 18px;
            border: 1px solid #e9ecef;
            margin-bottom: 20px;
            width: fit-content;
        }

        .typing-indicator.show {
            display: flex;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #667eea;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }

        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .input-group {
            flex: 1;
            position: relative;
        }

        .chat-input textarea {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 20px;
            font-size: 14px;
            resize: none;
            min-height: 44px;
            max-height: 120px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .chat-input textarea:focus {
            border-color: #667eea;
        }

        .send-button {
            background: #667eea;
            color: white;
            border: none;
            border-radius: 50%;
            width: 44px;
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .send-button:hover {
            background: #5a67d8;
        }

        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 8px;
            color: white;
            font-size: 14px;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: #28a745;
        }

        .notification.error {
            background: #dc3545;
        }

        .notification.info {
            background: #17a2b8;
        }

        .auth-section {
            padding: 20px;
            background: white;
            border-bottom: 1px solid #e9ecef;
        }

        .auth-form {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .auth-form input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .auth-form button {
            padding: 8px 16px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>AI Assistant Chat</h1>
            <p class="subtitle">Powered by OpenAI Assistant API</p>
            <div class="connection-status">
                <div class="status-dot" id="connectionDot"></div>
                <span id="connectionText">Disconnected</span>
            </div>
        </div>

        <div class="auth-section" id="authSection">
            <div class="auth-form">
                <input type="email" id="emailInput" placeholder="Email" required>
                <input type="password" id="passwordInput" placeholder="Password" required>
                <button onclick="login()">Login</button>
            </div>
        </div>

        <div class="trainer-selector hidden" id="trainerSelector">
            <select id="trainerSelect" onchange="selectTrainer()">
                <option value="">Select a trainer...</option>
            </select>
            <select id="submoduleSelect" onchange="selectSubmodule()">
                <option value="">Select submodule...</option>
            </select>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText">0%</div>
        </div>

        <div class="chat-messages hidden" id="chatMessages"></div>

        <div class="typing-indicator" id="typingIndicator">
            <span>Assistant is typing</span>
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        </div>

        <div class="chat-input hidden" id="chatInput">
            <div class="input-group">
                <textarea id="messageInput" placeholder="Type your message..." rows="1" onkeydown="handleKeyDown(event)" oninput="handleInput()"></textarea>
            </div>
            <button class="send-button" id="sendButton" onclick="sendMessage()">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                </svg>
            </button>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        class AssistantChat {
            constructor() {
                this.socket = null;
                this.authToken = null;
                this.currentThread = null;
                this.trainers = [];
                this.currentTrainer = null;
                this.currentSubmodule = null;
                this.isTyping = false;
                this.typingTimeout = null;
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.connectWebSocket();
            }

            setupEventListeners() {
                // Auto-resize textarea
                const messageInput = document.getElementById('messageInput');
                messageInput.addEventListener('input', () => {
                    messageInput.style.height = 'auto';
                    messageInput.style.height = messageInput.scrollHeight + 'px';
                });
            }

            connectWebSocket() {
                this.socket = io();
                
                this.socket.on('connect', () => {
                    this.updateConnectionStatus(true);
                    this.showNotification('Connected to server', 'success');
                });

                this.socket.on('disconnect', () => {
                    this.updateConnectionStatus(false);
                    this.showNotification('Disconnected from server', 'error');
                });

                this.socket.on('authenticated', (data) => {
                    if (data.success) {
                        this.showNotification('Authentication successful', 'success');
                        this.loadTrainers();
                    } else {
                        this.showNotification('Authentication failed', 'error');
                    }
                });

                this.socket.on('joined-assistant-conversation', (data) => {
                    this.showNotification(`Joined conversation for thread ${data.threadId}`, 'info');
                });

                this.socket.on('assistant-new-message', (data) => {
                    this.displayMessage(data.message, 'assistant', {
                        progress: data.progress,
                        score: data.score,
                        timestamp: data.timestamp
                    });
                    this.updateProgress(data.progress);
                });

                this.socket.on('assistant-typing', (data) => {
                    this.showTypingIndicator(data.isTyping);
                });

                this.socket.on('assistant-progress-updated', (data) => {
                    this.updateProgress(data.progress);
                    this.showNotification(`Progress updated: ${data.progress}%`, 'info');
                });

                this.socket.on('user-typing-start', (data) => {
                    // Handle other users typing (for group chats)
                });

                this.socket.on('user-typing-stop', (data) => {
                    // Handle other users stop typing
                });
            }

            updateConnectionStatus(connected) {
                const dot = document.getElementById('connectionDot');
                const text = document.getElementById('connectionText');
                
                if (connected) {
                    dot.classList.add('connected');
                    text.textContent = 'Connected';
                } else {
                    dot.classList.remove('connected');
                    text.textContent = 'Disconnected';
                }
            }

            async login() {
                const email = document.getElementById('emailInput').value;
                const password = document.getElementById('passwordInput').value;

                if (!email || !password) {
                    this.showNotification('Please enter email and password', 'error');
                    return;
                }

                try {
                    const response = await fetch('/api/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ email, password, userType: 'member' })
                    });

                    const data = await response.json();

                    if (data.success) {
                        this.authToken = data.token;
                        this.userId = data.user.id;
                        this.userName = data.user.name;
                        
                        // Authenticate with WebSocket
                        this.socket.emit('authenticate', {
                            token: this.authToken,
                            userId: this.userId,
                            role: 'member'
                        });

                        document.getElementById('authSection').classList.add('hidden');
                        document.getElementById('trainerSelector').classList.remove('hidden');
                        
                        this.showNotification(`Welcome, ${this.userName}!`, 'success');
                    } else {
                        this.showNotification(data.error || 'Login failed', 'error');
                    }
                } catch (error) {
                    console.error('Login error:', error);
                    this.showNotification('Login failed', 'error');
                }
            }

            async loadTrainers() {
                try {
                    const response = await fetch('/api/members/trainers', {
                        headers: {
                            'Authorization': `Bearer ${this.authToken}`
                        }
                    });

                    const data = await response.json();

                    if (data.success) {
                        this.trainers = data.trainers;
                        this.populateTrainerSelect();
                    } else {
                        this.showNotification('Failed to load trainers', 'error');
                    }
                } catch (error) {
                    console.error('Load trainers error:', error);
                    this.showNotification('Failed to load trainers', 'error');
                }
            }

            populateTrainerSelect() {
                const select = document.getElementById('trainerSelect');
                select.innerHTML = '<option value="">Select a trainer...</option>';
                
                this.trainers.forEach(trainer => {
                    const option = document.createElement('option');
                    option.value = trainer.id;
                    option.textContent = trainer.name;
                    select.appendChild(option);
                });
            }

            async selectTrainer() {
                const trainerId = document.getElementById('trainerSelect').value;
                
                if (!trainerId) {
                    document.getElementById('submoduleSelect').innerHTML = '<option value="">Select submodule...</option>';
                    return;
                }

                this.currentTrainer = this.trainers.find(t => t.id == trainerId);
                
                // Populate submodules
                const submoduleSelect = document.getElementById('submoduleSelect');
                submoduleSelect.innerHTML = '<option value="">Select submodule...</option>';
                
                if (this.currentTrainer.submodules) {
                    this.currentTrainer.submodules.forEach(submodule => {
                        const option = document.createElement('option');
                        option.value = submodule.id;
                        option.textContent = `${submodule.orderIndex + 1}. ${submodule.name}`;
                        submoduleSelect.appendChild(option);
                    });
                }
            }

            async selectSubmodule() {
                const submoduleId = document.getElementById('submoduleSelect').value;
                
                if (!submoduleId) {
                    return;
                }

                this.currentSubmodule = this.currentTrainer.submodules.find(s => s.id == submoduleId);
                
                // Initialize conversation
                await this.initializeConversation();
            }

            async initializeConversation() {
                try {
                    const response = await fetch('/api/assistant/conversation/initialize', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${this.authToken}`
                        },
                        body: JSON.stringify({
                            trainerId: this.currentTrainer.id,
                            submoduleId: this.currentSubmodule.id
                        })
                    });

                    const data = await response.json();

                    if (data.success) {
                        this.currentThread = data.data.thread;
                        this.updateProgress(data.data.currentProgress);
                        
                        // Join WebSocket room
                        this.socket.emit('join-assistant-conversation', {
                            threadId: this.currentThread.id,
                            memberId: this.userId
                        });

                        // Load conversation history
                        this.loadConversationHistory(data.data.conversationHistory);
                        
                        // Show chat interface
                        document.getElementById('chatMessages').classList.remove('hidden');
                        document.getElementById('chatInput').classList.remove('hidden');
                        
                        this.showNotification('Conversation initialized', 'success');
                    } else {
                        this.showNotification(data.error || 'Failed to initialize conversation', 'error');
                    }
                } catch (error) {
                    console.error('Initialize conversation error:', error);
                    this.showNotification('Failed to initialize conversation', 'error');
                }
            }

            loadConversationHistory(history) {
                const messagesContainer = document.getElementById('chatMessages');
                messagesContainer.innerHTML = '';

                history.forEach(message => {
                    this.displayMessage(message.content, message.role, {
                        progress: message.progress,
                        score: message.score,
                        timestamp: message.timestamp
                    });
                });

                // Scroll to bottom
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }

            displayMessage(content, role, meta = {}) {
                const messagesContainer = document.getElementById('chatMessages');
                
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${role}`;
                
                const avatar = document.createElement('div');
                avatar.className = 'message-avatar';
                avatar.textContent = role === 'user' ? 'U' : 'AI';
                
                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';
                
                const bubble = document.createElement('div');
                bubble.className = 'message-bubble';
                bubble.textContent = content;
                
                const metaDiv = document.createElement('div');
                metaDiv.className = 'message-meta';
                
                if (meta.timestamp) {
                    const timeSpan = document.createElement('span');
                    timeSpan.textContent = new Date(meta.timestamp).toLocaleTimeString();
                    metaDiv.appendChild(timeSpan);
                }
                
                if (meta.score !== null && meta.score !== undefined) {
                    const scoreSpan = document.createElement('span');
                    scoreSpan.className = 'score-badge';
                    scoreSpan.textContent = `Score: ${meta.score}`;
                    metaDiv.appendChild(scoreSpan);
                }
                
                contentDiv.appendChild(bubble);
                contentDiv.appendChild(metaDiv);
                
                messageDiv.appendChild(avatar);
                messageDiv.appendChild(contentDiv);
                
                messagesContainer.appendChild(messageDiv);
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }

            async sendMessage() {
                const messageInput = document.getElementById('messageInput');
                const message = messageInput.value.trim();
                
                if (!message || !this.currentThread) {
                    return;
                }

                // Display user message immediately
                this.displayMessage(message, 'user', {
                    timestamp: new Date().toISOString()
                });

                // Clear input
                messageInput.value = '';
                messageInput.style.height = 'auto';

                // Show typing indicator
                this.showTypingIndicator(true);

                try {
                    const response = await fetch('/api/assistant/message/send', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${this.authToken}`
                        },
                        body: JSON.stringify({
                            threadId: this.currentThread.id,
                            message: message
                        })
                    });

                    const data = await response.json();

                    if (data.success) {
                        // Hide typing indicator
                        this.showTypingIndicator(false);
                        
                        // Display assistant response
                        this.displayMessage(data.data.assistantResponse.content, 'assistant', {
                            progress: data.data.assistantResponse.progress,
                            score: data.data.assistantResponse.score,
                            timestamp: data.data.assistantResponse.timestamp
                        });

                        // Update progress
                        if (data.data.assistantResponse.progress !== null) {
                            this.updateProgress(data.data.assistantResponse.progress);
                        }
                    } else {
                        this.showTypingIndicator(false);
                        this.showNotification(data.error || 'Failed to send message', 'error');
                    }
                } catch (error) {
                    console.error('Send message error:', error);
                    this.showTypingIndicator(false);
                    this.showNotification('Failed to send message', 'error');
                }
            }

            updateProgress(progress) {
                if (progress === null || progress === undefined) return;
                
                const progressFill = document.getElementById('progressFill');
                const progressText = document.getElementById('progressText');
                
                progressFill.style.width = `${progress}%`;
                progressText.textContent = `${progress}%`;
            }

            showTypingIndicator(show) {
                const indicator = document.getElementById('typingIndicator');
                if (show) {
                    indicator.classList.add('show');
                } else {
                    indicator.classList.remove('show');
                }
            }

            handleKeyDown(event) {
                if (event.key === 'Enter' && !event.shiftKey) {
                    event.preventDefault();
                    this.sendMessage();
                }
            }

            handleInput() {
                // Handle typing indicators
                if (!this.isTyping && this.currentThread) {
                    this.isTyping = true;
                    this.socket.emit('typing-start', {
                        threadId: this.currentThread.id,
                        memberId: this.userId
                    });
                }

                // Clear previous timeout
                if (this.typingTimeout) {
                    clearTimeout(this.typingTimeout);
                }

                // Set new timeout
                this.typingTimeout = setTimeout(() => {
                    this.isTyping = false;
                    if (this.currentThread) {
                        this.socket.emit('typing-stop', {
                            threadId: this.currentThread.id,
                            memberId: this.userId
                        });
                    }
                }, 1000);
            }

            showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `notification ${type}`;
                notification.textContent = message;
                
                document.body.appendChild(notification);
                
                // Show notification
                setTimeout(() => {
                    notification.classList.add('show');
                }, 100);
                
                // Hide notification after 3 seconds
                setTimeout(() => {
                    notification.classList.remove('show');
                    setTimeout(() => {
                        document.body.removeChild(notification);
                    }, 300);
                }, 3000);
            }
        }

        // Initialize chat when page loads
        let chat;
        document.addEventListener('DOMContentLoaded', () => {
            chat = new AssistantChat();
        });

        // Global functions for HTML event handlers
        function login() {
            chat.login();
        }

        function selectTrainer() {
            chat.selectTrainer();
        }

        function selectSubmodule() {
            chat.selectSubmodule();
        }

        function sendMessage() {
            chat.sendMessage();
        }

        function handleKeyDown(event) {
            chat.handleKeyDown(event);
        }

        function handleInput() {
            chat.handleInput();
        }
    </script>
</body>
</html> 