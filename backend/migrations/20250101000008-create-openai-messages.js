'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('openai_messages', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      messageId: {
        type: Sequelize.STRING(255),
        allowNull: false,
        unique: true,
        comment: 'OpenAI message ID'
      },
      threadId: {
        type: Sequelize.STRING(255),
        allowNull: false,
        references: {
          model: 'openai_threads',
          key: 'threadId'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      runId: {
        type: Sequelize.STRING(255),
        allowNull: true,
        comment: 'OpenAI run ID'
      },
      role: {
        type: Sequelize.ENUM('user', 'assistant', 'system'),
        allowNull: false
      },
      content: {
        type: Sequelize.TEXT,
        allowNull: false
      },
      aiProgress: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: 'AI-assessed progress (0-100)'
      },
      aiScore: {
        type: Sequelize.DECIMAL(5, 2),
        allowNull: true,
        comment: 'AI-assessed score'
      },
      status: {
        type: Sequelize.ENUM('pending', 'completed', 'failed'),
        defaultValue: 'completed',
        allowNull: false
      },
      metadata: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: 'Additional message metadata'
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // Add indexes
    await queryInterface.addIndex('openai_messages', ['messageId']);
    await queryInterface.addIndex('openai_messages', ['threadId', 'role', 'created_at']);
    await queryInterface.addIndex('openai_messages', ['runId']);
    await queryInterface.addIndex('openai_messages', ['aiProgress']);
    await queryInterface.addIndex('openai_messages', ['aiScore']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('openai_messages');
  }
}; 