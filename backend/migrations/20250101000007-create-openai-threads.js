'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('openai_threads', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      threadId: {
        type: Sequelize.STRING(255),
        allowNull: false,
        unique: true,
        comment: 'OpenAI thread ID'
      },
      trainerId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'trainers',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      submoduleId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'trainer_submodules',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      memberId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'members',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      assistantId: {
        type: Sequelize.STRING(255),
        allowNull: true,
        comment: 'OpenAI assistant ID'
      },
      status: {
        type: Sequelize.ENUM('active', 'inactive', 'archived'),
        defaultValue: 'active',
        allowNull: false
      },
      lastMessageAt: {
        type: Sequelize.DATE,
        allowNull: true
      },
      metadata: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: 'Additional thread metadata'
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // Add indexes
    await queryInterface.addIndex('openai_threads', ['threadId']);
    await queryInterface.addIndex('openai_threads', ['trainerId', 'memberId', 'status']);
    await queryInterface.addIndex('openai_threads', ['submoduleId']);
    await queryInterface.addIndex('openai_threads', ['status']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('openai_threads');
  }
}; 