'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Check if column exists before adding
    const tableInfo = await queryInterface.describeTable('trainer_scores');
    
    if (!tableInfo.submoduleId) {
      await queryInterface.addColumn('trainer_scores', 'submoduleId', {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'trainer_submodules',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      });

      // Add index for submoduleId
      await queryInterface.addIndex('trainer_scores', ['submoduleId']);
    }
  },

  async down(queryInterface, Sequelize) {
    // Remove the column if it exists
    const tableInfo = await queryInterface.describeTable('trainer_scores');
    
    if (tableInfo.submoduleId) {
      await queryInterface.removeColumn('trainer_scores', 'submoduleId');
    }
  }
}; 