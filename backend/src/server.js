const { createServer } = require('http');
const { Server } = require('socket.io');
const config = require('./config');
const { sequelize } = require('./models');
const logger = require('./utils/logger');
const webSocketService = require('./services/websocket.service');
const cacheService = require('./services/cache.service');
const app = require('./app');

const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: config.cors.origin,
    methods: ['GET', 'POST']
  }
});

// Initialize WebSocket service
webSocketService.initialize(io);

// Database connection and server startup
const startServer = async () => {
  try {
    // Test database connection
    await sequelize.authenticate();
    logger.info('Database connection established successfully');
    // Sync database models (in development) - safer approach
    if (config.nodeEnv === 'development') {
      await sequelize.sync({ force: false });
      logger.info('Database models synchronized');
    }
    // Initialize cache service
    if (config.cache.enabled) {
      await cacheService.initialize();
      logger.info('Cache service initialized successfully');
    }
    // Start server
    server.listen(config.port, () => {
      logger.info(`Server running on port ${config.port} in ${config.nodeEnv} mode`);
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
};

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  await sequelize.close();
  await cacheService.close();
  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully');
  await sequelize.close();
  await cacheService.close();
  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
});

module.exports = { server, io, startServer }; 