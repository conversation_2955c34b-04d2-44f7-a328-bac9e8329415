const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');

const config = require('./config');
const logger = require('./utils/logger');
const swagger = require('./config/swagger');
const {
  globalErrorHandler,
  addCorrelationId,
  notFoundHandler
} = require('./middleware/error.middleware');

// Import routes
const authRoutes = require('./routes/auth');
const adminRoutes = require('./routes/admin');
const trainerRoutes = require('./routes/trainers');
const trainerSubmoduleRoutes = require('./routes/trainer-submodules');
const chatRoutes = require('./routes/chat');
// const assistantRoutes = require('./routes/assistant.routes');
// const notificationRoutes = require('./routes/notification.routes');
const progressRoutes = require('./routes/progress');
const monitoringRoutes = require('./routes/monitoring');
const systemRoutes = require('./routes/system');
const memberRoutes = require('./routes/members');

const app = express();

// Security middleware
app.use(helmet());

// CORS configuration
app.use(cors({
  origin: config.cors.origin,
  credentials: true
}));

// Add correlation ID to all requests
app.use(addCorrelationId);

// Rate limiting
const limiter = rateLimit({
  windowMs: config.rateLimit.windowMs,
  max: config.rateLimit.maxRequests,
  message: 'Too many requests from this IP, please try again later.'
});
app.use(limiter);

// Compression middleware
app.use(compression());

// Logging middleware
app.use(morgan('combined', {
  stream: {
    write: (message) => logger.info(message.trim())
  }
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Serve static files
app.use(express.static('public'));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    environment: config.nodeEnv
  });
});

// API Documentation
app.use('/api-docs', swagger.serve, swagger.setup);

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/admins', adminRoutes);
app.use('/api/trainers', trainerRoutes);
app.use('/api', trainerSubmoduleRoutes);
app.use('/api/chat', chatRoutes);
// app.use('/api/assistant', assistantRoutes);
// app.use('/api/notifications', notificationRoutes);
app.use('/api/progress', progressRoutes);
app.use('/api/monitoring', monitoringRoutes);
app.use('/api/system', systemRoutes);
app.use('/api/members', memberRoutes);

// Handle 404 routes
app.use(notFoundHandler);

// Global error handling middleware (must be last)
app.use(globalErrorHandler);

module.exports = app;