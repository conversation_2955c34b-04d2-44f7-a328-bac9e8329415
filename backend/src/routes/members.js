const express = require('express');
const router = express.Router();
const memberController = require('../controllers/member.controller');
const authMiddleware = require('../middleware/auth.middleware');

// Reset password member oleh admin
router.post('/:id/reset-password', authMiddleware.authenticateToken, authMiddleware.requireAdmin, memberController.resetPassword);

// GET all members (for admin/superadmin)
router.get(
  '/',
  authMiddleware.authenticateToken,
  authMiddleware.requireAdmin, // atau requireSuperAdmin jika perlu
  memberController.getAllMembers
);

module.exports = router; 