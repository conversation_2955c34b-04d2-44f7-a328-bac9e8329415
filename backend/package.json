{"name": "ai-trainer-platform", "version": "1.0.0", "description": "AI-powered training management system with OpenAI Assistant integration", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test:watch": "jest --watch --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test:coverage": "jest --coverage --detect<PERSON><PERSON><PERSON><PERSON><PERSON>", "test:integration": "jest --testPathPattern=integration --detectO<PERSON>Handles", "test:unit": "jest --testPathPattern=unit --detectOpenHandles", "db:migrate": "npx sequelize-cli db:migrate", "db:seed": "npx sequelize-cli db:seed:all"}, "dependencies": {"bcrypt": "^5.1.1", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "envalid": "^8.1.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.2.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "mysql2": "^3.6.5", "openai": "^5.9.2", "redis": "^4.6.11", "sequelize": "^6.35.2", "socket.io": "^4.7.4", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/jest": "^29.5.8", "jest": "^29.7.0", "nodemon": "^3.0.2", "sqlite3": "^5.1.6", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}, "keywords": ["ai", "training", "openai", "education", "lms"], "author": "AI Trainer Platform Team", "license": "MIT"}